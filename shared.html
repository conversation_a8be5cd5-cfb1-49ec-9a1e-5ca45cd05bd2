<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shared Div Example</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .shared-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .shared-header {
            background: #458FF6;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .shared-content {
            padding: 30px;
        }

        .shared-item {
            background: #f8f9fa;
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #458FF6;
        }

        .shared-title {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 10px;
        }

        .shared-text {
            color: #666;
            line-height: 1.6;
        }

        .btn-share {
            background: #458FF6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }

        .btn-share:hover {
            background: #357abd;
        }
    </style>
</head>
<body>
    <div class="shared-container">
        <div class="shared-header">
            <h1>Shared Content</h1>
            <p>مشاركة المحتوى بطريقة بسيطة</p>
        </div>
        
        <div class="shared-content">
            <div class="shared-item">
                <h3 class="shared-title">معلومات طبية</h3>
                <p class="shared-text">هذا محتوى طبي مشترك يمكن مشاركته مع الأطباء والمرضى</p>
                <button class="btn-share">مشاركة</button>
            </div>

            <div class="shared-item">
                <h3 class="shared-title">نصائح صحية</h3>
                <p class="shared-text">نصائح مهمة للحفاظ على الصحة العامة</p>
                <button class="btn-share">مشاركة</button>
            </div>

            <div class="shared-item">
                <h3 class="shared-title">مواعيد الأطباء</h3>
                <p class="shared-text">جدول مواعيد الأطباء المتاحين للاستشارة</p>
                <button class="btn-share">مشاركة</button>
            </div>

            <div class="shared-item">
                <h3 class="shared-title">الأدوية</h3>
                <p class="shared-text">قائمة بالأدوية المتوفرة في الصيدلية الإلكترونية</p>
                <button class="btn-share">مشاركة</button>
            </div>
        </div>
    </div>

    <script>
        // بسكريبت بسيط للمشاركة
        document.querySelectorAll('.btn-share').forEach(button => {
            button.addEventListener('click', function() {
                const item = this.closest('.shared-item');
                const title = item.querySelector('.shared-title').textContent;
                alert('تم مشاركة: ' + title);
            });
        });
    </script>
</body>
</html>
