/* Box model reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    padding: 20px;
}

/* Header with padding and border */
.header {
    padding: 15px 30px;
    border: 2px solid #458FF6;
    border-width: 3px;
    margin-bottom: 20px;
}

/* Logo with box shadow */
.logo {
    display: inline-block;
    padding: 10px 15px;
    border: 1px solid #ccc;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.logo-icon {
    background: #458FF6;
    color: white;
    padding: 8px 12px;
    border-radius: 50%;
    margin-right: 10px;
}

/* Navigation with borders */
.nav-menu {
    list-style: none;
    display: flex;
    gap: 20px;
    padding: 10px 0;
    border-top: 2px solid #eee;
    border-bottom: 2px solid #eee;
    margin-top: 15px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    padding: 8px 15px;
    border: 1px solid transparent;
}

.nav-link:hover {
    border: 1px solid #458FF6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Hero section with padding and box shadow */
.hero {
    background: #f0f8ff;
    padding: 40px 30px;
    border: 3px solid #458FF6;
    border-width: 2px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
    margin: 30px 0;
}

.hero-title {
    font-size: 2.5rem;
    color: #333;
    padding: 10px 0;
    border-bottom: 3px solid #458FF6;
    margin-bottom: 20px;
}

.hero-description {
    padding: 15px 0;
    font-size: 1.1rem;
    color: #666;
}

/* Button with padding and box shadow */
.btn-primary {
    background: #458FF6;
    color: white;
    border: none;
    border-width: 0;
    padding: 12px 25px;
    font-size: 1rem;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(69, 143, 246, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 6px 12px rgba(69, 143, 246, 0.4);
}

/* Services section */
.services {
    padding: 40px 20px;
    border: 1px solid #ddd;
    border-width: 2px;
}

.services-header {
    text-align: center;
    padding: 20px 0;
    border-bottom: 2px solid #458FF6;
    margin-bottom: 30px;
}

.section-title {
    font-size: 2rem;
    color: #333;
    padding: 10px 0;
}

/* Service cards with box model properties */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.service-card {
    background: white;
    padding: 25px 20px;
    border: 2px solid #eee;
    border-width: 1px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    text-align: center;
}

.service-card:hover {
    border: 2px solid #458FF6;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.service-icon {
    font-size: 2.5rem;
    padding: 15px 0;
}

.service-title {
    font-size: 1.3rem;
    color: #333;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.service-description {
    color: #666;
    padding: 10px 0;
}