* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Header */
.header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    background: #458FF6;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: bold;
    color: #233348;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #1F1534;
    font-weight: 400;
}

.nav-link:hover {
    color: #458FF6;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #67B6FF 0%, #458FF6 100%);
    padding: 4rem 2rem;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    color: white;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-description {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-primary {
    background: white;
    color: #458FF6;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.hero-image {
    font-size: 4rem;
    text-align: center;
}

/* Services Section */
.services {
    padding: 4rem 2rem;
    background: white;
}

.services-header {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 3rem;
}

.section-title {
    font-size: 2.5rem;
    color: #233348;
    margin-bottom: 1rem;
}

.title-underline {
    width: 60px;
    height: 4px;
    background: #458FF6;
    margin: 0 auto 1.5rem;
    border-radius: 2px;
}

.section-description {
    color: #7D7987;
    font-size: 1.1rem;
    line-height: 1.8;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-title {
    font-size: 1.5rem;
    color: #233348;
    margin-bottom: 1rem;
}

.service-description {
    color: #7D7987;
    line-height: 1.6;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .nav-menu {
        display: none;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}